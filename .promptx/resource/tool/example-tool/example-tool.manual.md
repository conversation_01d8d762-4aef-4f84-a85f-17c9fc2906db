# Example Tool 使用手册

## 工具概述
这是一个PromptX工具创建示例，演示如何创建和注册自定义工具。

## 功能说明
- 接收文本消息并进行处理
- 将消息转换为大写格式
- 返回处理结果和统计信息

## 参数说明

### 必需参数
- `message` (string): 要处理的消息内容

### 可选参数
- `options` (object): 可选配置参数

## 使用示例

### 基础用法
```javascript
{
  "message": "hello world"
}
```

### 返回结果
```javascript
{
  "success": true,
  "message": "处理完成: hello world",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "input": "hello world",
    "processed": "HELLO WORLD",
    "length": 11
  }
}
```

## 错误处理
如果缺少必需参数，工具会返回错误信息：
```javascript
{
  "success": false,
  "error": "缺少必需参数: message",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 注意事项
1. 确保提供必需的 `message` 参数
2. 工具会自动处理异常情况
3. 返回结果包含时间戳便于调试

## 版本信息
- 版本: 1.0.0
- 作者: PromptX User
- 创建时间: 2024年
