/**
 * PromptX工具示例
 * 工具名称: example-tool
 * 功能: 演示如何创建PromptX工具
 */

// 工具主函数 - 必须导出
async function main(params) {
    try {
        // 参数验证
        if (!params.message) {
            throw new Error('缺少必需参数: message');
        }

        // 工具核心逻辑
        const result = {
            success: true,
            message: `处理完成: ${params.message}`,
            timestamp: new Date().toISOString(),
            data: {
                input: params.message,
                processed: params.message.toUpperCase(),
                length: params.message.length
            }
        };

        return result;

    } catch (error) {
        return {
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        };
    }
}

// 工具元数据 - 必须导出
const metadata = {
    name: 'example-tool',
    version: '1.0.0',
    description: 'PromptX工具创建示例',
    author: 'PromptX User',
    parameters: {
        message: {
            type: 'string',
            required: true,
            description: '要处理的消息内容'
        },
        options: {
            type: 'object',
            required: false,
            description: '可选配置参数'
        }
    }
};

// 导出工具 - PromptX需要execute方法
module.exports = {
    execute: main,  // PromptX调用execute方法
    main,
    metadata
};
