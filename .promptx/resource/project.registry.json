{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-20T13:06:50.244Z", "updatedAt": "2025-07-20T13:06:50.246Z", "resourceCount": 3}, "resources": [{"id": "riper6-workflow", "source": "project", "protocol": "execution", "name": "Riper6 Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/riper6-dev/execution/riper6-workflow.execution.md", "metadata": {"createdAt": "2025-07-20T13:06:50.245Z", "updatedAt": "2025-07-20T13:06:50.245Z", "scannedAt": "2025-07-20T13:06:50.245Z", "path": "role/riper6-dev/execution/riper6-workflow.execution.md"}}, {"id": "riper6-dev", "source": "project", "protocol": "role", "name": "Riper6 Dev 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/riper6-dev/riper6-dev.role.md", "metadata": {"createdAt": "2025-07-20T13:06:50.245Z", "updatedAt": "2025-07-20T13:06:50.245Z", "scannedAt": "2025-07-20T13:06:50.245Z", "path": "role/riper6-dev/riper6-dev.role.md"}}, {"id": "riper6-thinking", "source": "project", "protocol": "thought", "name": "Riper6 Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/riper6-dev/thought/riper6-thinking.thought.md", "metadata": {"createdAt": "2025-07-20T13:06:50.246Z", "updatedAt": "2025-07-20T13:06:50.246Z", "scannedAt": "2025-07-20T13:06:50.246Z", "path": "role/riper6-dev/thought/riper6-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}