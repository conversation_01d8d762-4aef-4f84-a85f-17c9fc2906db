# MCP工具功能清单

> **文档版本**: v1.0  
> **创建时间**: 2025-01-20  
> **维护者**: 女娲 (PromptX角色创造专家)  
> **用途**: 为MCP工具管家角色提供完整的工具能力参考

## 📋 工具总览

当前环境共有 **50+** 个MCP工具，按功能分为8大类别：

| 类别 | 工具数量 | 主要用途 | 与riper6-dev协同度 |
|------|----------|----------|-------------------|
| 📚 文档和库查询 | 2 | 技术文档获取 | ⭐⭐⭐⭐⭐ |
| 🌐 网页抓取 | 6 | 数据采集分析 | ⭐⭐⭐⭐ |
| 🎭 浏览器自动化 | 7 | UI测试自动化 | ⭐⭐⭐⭐⭐ |
| 🎯 任务管理 | 15+ | 项目管理协同 | ⭐⭐⭐⭐⭐ |
| 🔧 n8n工作流 | 15+ | 自动化流程 | ⭐⭐⭐ |
| 💭 思维和记忆 | 3 | 智能分析决策 | ⭐⭐⭐⭐ |
| 📁 文件系统 | 10+ | 代码文件操作 | ⭐⭐⭐⭐⭐ |
| 🎨 可视化和其他 | 5+ | 图表代码分析 | ⭐⭐⭐ |

---

## 📚 文档和库查询工具

### 1. `resolve-library-id_context7`
- **功能**: 解析包/产品名称为Context7兼容的库ID
- **核心参数**: 
  - `libraryName`* (string): 要搜索的库名称
- **使用场景**: 获取准确的库标识符，为后续文档查询做准备
- **协同关系**: 与`get-library-docs_context7`配合使用
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (研究模式必备)

### 2. `get-library-docs_context7`
- **功能**: 获取库的最新文档内容
- **核心参数**:
  - `context7CompatibleLibraryID`* (string): 库ID
  - `tokens` (number): 文档token数量限制
  - `topic` (string): 聚焦的主题
- **使用场景**: 获取技术栈最新文档和注意事项
- **协同关系**: 需要先使用`resolve-library-id_context7`
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (研究模式核心工具)

---

## 🌐 网页抓取工具

### 3. `fetch_html_fetch`
- **功能**: 获取网页HTML内容
- **核心参数**:
  - `url`* (string): 目标网页URL
  - `headers` (object): 可选HTTP头
- **使用场景**: 网页结构分析、内容提取
- **协同关系**: 与其他fetch工具互补
- **riper6-dev协同**: ⭐⭐⭐⭐ (研究模式数据收集)

### 4. `fetch_markdown_fetch`
- **功能**: 获取网页内容并转换为Markdown格式
- **核心参数**:
  - `url`* (string): 目标网页URL
  - `headers` (object): 可选HTTP头
- **使用场景**: 文档整理、内容分析
- **协同关系**: 比HTML更适合文本处理
- **riper6-dev协同**: ⭐⭐⭐⭐ (文档研究首选)

### 5. `fetch_txt_fetch`
- **功能**: 获取网页纯文本内容(无HTML)
- **核心参数**:
  - `url`* (string): 目标网页URL
  - `headers` (object): 可选HTTP头
- **使用场景**: 纯文本分析、内容提取
- **协同关系**: 最轻量的内容获取方式
- **riper6-dev协同**: ⭐⭐⭐ (简单内容分析)

### 6. `fetch_json_fetch`
- **功能**: 获取JSON数据
- **核心参数**:
  - `url`* (string): JSON数据URL
  - `headers` (object): 可选HTTP头
- **使用场景**: API数据获取、配置文件下载
- **协同关系**: 与API开发工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐ (API研究分析)

### 7. `web-search`
- **功能**: 网页搜索(Google Custom Search API)
- **核心参数**:
  - `query`* (string): 搜索查询
  - `num_results` (number): 结果数量(1-10)
- **使用场景**: 技术问题搜索、解决方案查找
- **协同关系**: 与fetch工具组合使用
- **riper6-dev协同**: ⭐⭐⭐⭐ (研究模式信息收集)

### 8. `web-fetch`
- **功能**: 获取网页内容并转换为Markdown
- **核心参数**:
  - `url`* (string): 目标网页URL
- **使用场景**: 网页内容分析、文档整理
- **协同关系**: 与web-search配合使用
- **riper6-dev协同**: ⭐⭐⭐⭐ (内容研究分析)

---

## 🎭 浏览器自动化工具

### 9. `puppeteer_navigate_puppeteer`
- **功能**: 浏览器导航到指定URL
- **核心参数**:
  - `url`* (string): 目标URL
  - `launchOptions` (object): Puppeteer启动选项
  - `allowDangerous` (boolean): 允许危险选项
- **使用场景**: 自动化测试、页面交互准备
- **协同关系**: 其他puppeteer工具的前置步骤
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式UI测试)

### 10. `puppeteer_screenshot_puppeteer`
- **功能**: 截取页面或元素截图
- **核心参数**:
  - `name`* (string): 截图名称
  - `selector` (string): CSS选择器
  - `width` (number): 宽度(默认800)
  - `height` (number): 高度(默认600)
  - `encoded` (boolean): 是否base64编码
- **使用场景**: UI测试验证、页面状态记录
- **协同关系**: 与其他puppeteer工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (审核模式验证)

### 11. `puppeteer_click_puppeteer`
- **功能**: 点击页面元素
- **核心参数**:
  - `selector`* (string): CSS选择器
- **使用场景**: 按钮点击、链接跳转、表单提交
- **协同关系**: 与fill、select工具组合使用
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式交互操作)

### 12. `puppeteer_fill_puppeteer`
- **功能**: 填写输入字段
- **核心参数**:
  - `selector`* (string): 输入框CSS选择器
  - `value`* (string): 填入的值
- **使用场景**: 表单填写、搜索输入、数据录入
- **协同关系**: 与click工具配合完成表单操作
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式数据输入)

### 13. `puppeteer_select_puppeteer`
- **功能**: 选择下拉框选项
- **核心参数**:
  - `selector`* (string): 下拉框CSS选择器
  - `value`* (string): 要选择的值
- **使用场景**: 下拉菜单选择、选项配置
- **协同关系**: 与其他表单操作工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式选项操作)

### 14. `puppeteer_hover_puppeteer`
- **功能**: 鼠标悬停在元素上
- **核心参数**:
  - `selector`* (string): CSS选择器
- **使用场景**: 触发悬停效果、显示隐藏菜单
- **协同关系**: 与其他交互工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐ (执行模式交互测试)

### 15. `puppeteer_evaluate_puppeteer`
- **功能**: 在浏览器控制台执行JavaScript
- **核心参数**:
  - `script`* (string): 要执行的JavaScript代码
- **使用场景**: 页面数据提取、DOM操作、状态检查
- **协同关系**: 最灵活的浏览器操作工具
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式高级操作)

---

## 🎯 任务管理工具 (Task Master)

### 16. `initialize_project_task-master`
- **功能**: 初始化Task Master项目结构
- **核心参数**:
  - `projectRoot`* (string): 项目根目录绝对路径
  - `rules` (array): 规则配置文件
  - `initGit` (boolean): 是否初始化Git
  - `addAliases` (boolean): 是否添加shell别名
- **使用场景**: 新项目启动、任务管理系统搭建
- **协同关系**: 其他task-master工具的前置条件
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (规划模式项目初始化)

### 17. `get_tasks_task-master`
- **功能**: 获取所有任务列表
- **核心参数**:
  - `projectRoot`* (string): 项目根目录
  - `status` (string): 按状态过滤
  - `withSubtasks` (boolean): 包含子任务
- **使用场景**: 任务状态查看、进度跟踪
- **协同关系**: 与任务操作工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (所有模式任务查看)

### 18. `add_task_task-master`
- **功能**: 使用AI添加新任务
- **核心参数**:
  - `projectRoot`* (string): 项目根目录
  - `prompt` (string): 任务描述
  - `priority` (string): 任务优先级
  - `dependencies` (string): 依赖任务ID
- **使用场景**: 智能任务创建、需求转换
- **协同关系**: 与任务管理工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (规划模式任务创建)

### 19. `expand_task_task-master`
- **功能**: 将任务展开为子任务
- **核心参数**:
  - `id`* (string): 要展开的任务ID
  - `projectRoot`* (string): 项目根目录
  - `num` (string): 子任务数量
  - `research` (boolean): 使用研究模式
- **使用场景**: 复杂任务分解、详细实施规划
- **协同关系**: 与复杂度分析工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (规划模式任务分解)

### 20. `set_task_status_task-master`
- **功能**: 设置任务或子任务状态
- **核心参数**:
  - `id`* (string): 任务ID(支持子任务格式15.2)
  - `status`* (string): 新状态(pending/done/in-progress等)
  - `projectRoot`* (string): 项目根目录
- **使用场景**: 任务进度更新、状态跟踪
- **协同关系**: 与任务查看工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式状态更新)

### 21. `research_task-master`
- **功能**: AI驱动的研究查询
- **核心参数**:
  - `query`* (string): 研究查询内容
  - `projectRoot`* (string): 项目根目录
  - `taskIds` (string): 相关任务ID
  - `detailLevel` (string): 详细程度(low/medium/high)
- **使用场景**: 技术调研、问题解决方案研究
- **协同关系**: 与任务更新工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (研究模式核心工具)

---

## 🔧 n8n工作流工具

### 22. `list_nodes_n8n-mcp`
- **功能**: 列出n8n节点
- **核心参数**:
  - `limit` (number): 最大结果数(默认50)
  - `category` (string): 节点类别过滤
  - `package` (string): 包名过滤
- **使用场景**: 节点发现、工作流设计准备
- **协同关系**: 与节点信息获取工具配合
- **riper6-dev协同**: ⭐⭐⭐ (规划模式自动化设计)

### 23. `get_node_info_n8n-mcp`
- **功能**: 获取节点完整schema信息
- **核心参数**:
  - `nodeType`* (string): 完整节点类型(如nodes-base.httpRequest)
- **使用场景**: 节点详细配置、参数了解
- **协同关系**: 与节点搜索工具配合
- **riper6-dev协同**: ⭐⭐⭐ (研究模式节点分析)

### 24. `search_nodes_n8n-mcp`
- **功能**: 按关键词搜索节点
- **核心参数**:
  - `query`* (string): 搜索关键词
  - `mode` (string): 搜索模式(OR/AND/FUZZY)
  - `limit` (number): 结果数量
- **使用场景**: 功能节点查找、解决方案发现
- **协同关系**: 与节点信息工具配合
- **riper6-dev协同**: ⭐⭐⭐ (研究模式功能探索)

### 25. `n8n_create_workflow_n8n-mcp`
- **功能**: 创建n8n工作流
- **核心参数**:
  - `name`* (string): 工作流名称
  - `nodes`* (array): 节点数组
  - `connections`* (object): 连接配置
- **使用场景**: 自动化流程创建、工作流部署
- **协同关系**: 需要节点配置知识
- **riper6-dev协同**: ⭐⭐⭐ (执行模式自动化实施)

---

## 💭 思维和记忆工具

### 26. `sequentialthinking_Sequential_thinking`
- **功能**: 序列化思维分析工具
- **核心参数**:
  - `thought`* (string): 当前思维步骤
  - `nextThoughtNeeded`* (boolean): 是否需要下一步
  - `thoughtNumber`* (integer): 思维步骤编号
  - `totalThoughts`* (integer): 预估总步骤
- **使用场景**: 复杂问题分析、逐步推理
- **协同关系**: 独立的思维分析工具
- **riper6-dev协同**: ⭐⭐⭐⭐ (所有模式复杂分析)

### 27. `remember`
- **功能**: 记忆重要信息
- **核心参数**:
  - `memory`* (string): 要记忆的信息(简洁1句话)
- **使用场景**: 长期信息存储、经验积累
- **协同关系**: 与项目管理工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐ (所有模式经验记录)

---

## 📁 文件系统工具

### 28. `read_file_file-system`
- **功能**: 读取文件完整内容
- **核心参数**:
  - `path`* (string): 文件路径
  - `head` (number): 只读前N行
  - `tail` (number): 只读后N行
- **使用场景**: 代码审查、配置文件查看
- **协同关系**: 与编辑工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (研究模式文件分析)

### 29. `write_file_file-system`
- **功能**: 创建新文件或完全覆盖
- **核心参数**:
  - `path`* (string): 文件路径
  - `content`* (string): 文件内容
  - `add_last_line_newline` (boolean): 添加末尾换行
- **使用场景**: 新文件创建、模板生成
- **协同关系**: 与编辑工具互补
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式文件创建)

### 30. `edit_file_file-system`
- **功能**: 基于行的文件编辑
- **核心参数**:
  - `path`* (string): 文件路径
  - `edits`* (array): 编辑操作数组
  - `dryRun` (boolean): 预览模式
- **使用场景**: 精确代码修改、配置更新
- **协同关系**: 与读取工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (执行模式精确修改)

### 31. `list_directory_file-system`
- **功能**: 列出目录内容
- **核心参数**:
  - `path`* (string): 目录路径
- **使用场景**: 项目结构了解、文件发现
- **协同关系**: 与搜索工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (研究模式结构分析)

### 32. `search_files_file-system`
- **功能**: 递归搜索文件
- **核心参数**:
  - `path`* (string): 搜索起始路径
  - `pattern`* (string): 搜索模式
  - `excludePatterns` (array): 排除模式
- **使用场景**: 文件查找、代码定位
- **协同关系**: 与读取工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (研究模式文件发现)

---

## 🎨 可视化和其他工具

### 33. `render-mermaid`
- **功能**: 渲染Mermaid图表
- **核心参数**:
  - `diagram_definition`* (string): Mermaid图表定义
  - `title` (string): 图表标题
- **使用场景**: 流程图创建、架构图展示
- **协同关系**: 与文档工具配合
- **riper6-dev协同**: ⭐⭐⭐ (规划模式可视化)

### 34. `codebase-retrieval`
- **功能**: 代码库上下文检索
- **核心参数**:
  - `information_request`* (string): 信息请求描述
- **使用场景**: 代码理解、相关代码发现
- **协同关系**: 与文件系统工具配合
- **riper6-dev协同**: ⭐⭐⭐⭐⭐ (研究模式代码分析)

---

## 🎯 工具协同使用建议

### 与riper6-dev协同的最佳工具组合

#### [MODE: RES] 研究模式推荐组合
1. **技术调研链**: `web-search` → `web-fetch` → `resolve-library-id_context7` → `get-library-docs_context7`
2. **代码分析链**: `codebase-retrieval` → `read_file_file-system` → `list_directory_file-system`
3. **任务研究链**: `research_task-master` → `get_tasks_task-master`

#### [MODE: PLAN] 规划模式推荐组合
1. **项目规划链**: `initialize_project_task-master` → `add_task_task-master` → `expand_task_task-master`
2. **架构设计链**: `render-mermaid` + `sequentialthinking_Sequential_thinking`

#### [MODE: EXE] 执行模式推荐组合
1. **文件操作链**: `write_file_file-system` → `edit_file_file-system` → `read_file_file-system`
2. **UI测试链**: `puppeteer_navigate_puppeteer` → `puppeteer_fill_puppeteer` → `puppeteer_click_puppeteer` → `puppeteer_screenshot_puppeteer`
3. **任务执行链**: `set_task_status_task-master` → `get_tasks_task-master`

#### [MODE: REV] 审核模式推荐组合
1. **代码审核链**: `codebase-retrieval` → `read_file_file-system` → `puppeteer_screenshot_puppeteer`
2. **任务验证链**: `get_tasks_task-master` → `research_task-master`

---

## 📝 使用最佳实践

### 1. 工具选择原则
- **精确性优先**: 优先选择能精确完成任务的专用工具
- **组合使用**: 多个简单工具组合比单个复杂工具更可控
- **状态保持**: 注意工具间的状态传递和数据一致性

### 2. 错误处理策略
- **预检查**: 使用list/search工具先确认目标存在
- **渐进式**: 从简单操作开始，逐步增加复杂度
- **回滚准备**: 重要操作前先备份或记录状态

### 3. 性能优化建议
- **批量操作**: 优先使用支持批量的工具
- **缓存利用**: 重复信息优先从本地文件获取
- **并行处理**: 独立操作可以并行执行

---

**文档状态**: ✅ 第一阶段完成 - MCP工具清单已创建  
**下一步**: 基于此清单设计MCP工具管家角色
