# 基于n8n + MCP-Chrome的智能网站监控方案

## 方案概述

采用n8n工作流平台结合MCP-Chrome浏览器自动化工具，构建智能的政府采购网站监控系统。通过MCP Client Tool节点调用mcp-chrome服务，实现真实浏览器环境下的内容抓取和数据提取。

## 技术架构

```
Schedule Trigger → AI Agent → MCP Client Tool → mcp-chrome → 数据处理 → 飞书API → 存储
```

### 核心组件

1. **n8n工作流引擎**：任务调度和流程编排
2. **MCP Client Tool节点**：连接MCP服务器的桥梁
3. **mcp-chrome服务**：Chrome扩展形式的MCP服务器
4. **AI Agent节点**：智能内容分析和数据提取
5. **飞书多维表格API**：结构化数据存储

## 详细技术实现

### 1. MCP-Chrome服务配置

**安装和启动**：
```bash
# 安装Chrome扩展
# 从Chrome Web Store安装Browser MCP扩展
# 或从GitHub下载mcp-chrome项目

# 启动MCP服务器
npx mcp-chrome-server --port 3001
```

**功能特性**：
- ✅ 真实Chrome浏览器环境
- ✅ JavaScript完全渲染支持
- ✅ 页面导航和交互能力
- ✅ 内容提取和截图功能
- ✅ 反爬虫机制绕过

### 2. n8n工作流设计

#### 2.1 定时触发器配置
```yaml
Schedule Trigger:
  Trigger Interval: "Custom (Cron)"
  Expression: "0 */30 8-18 * * 1-5"  # 工作日8-18点，每30分钟
```

#### 2.2 AI Agent节点配置
```yaml
AI Agent:
  Model: "Claude 3.5 Sonnet"
  System Prompt: |
    你是一个专业的政府采购信息提取专家。
    任务：监控浙江政府采购网，提取招标公告和中标结果信息。
    
    目标网站：
    1. 招标公告：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement
    2. 中标结果：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyResult
    
    提取字段：
    - 公告标题
    - 发布时间
    - 采购单位
    - 项目预算
    - 公告详情链接
    - 公告类型（招标/中标）
    
    输出格式：JSON数组
```

#### 2.3 MCP Client Tool节点配置
```yaml
MCP Client Tool:
  SSE Endpoint: "http://localhost:3001/sse"
  Authentication: "None"
  Tools to Include: "All"
```

### 3. 浏览器自动化流程

#### 3.1 页面导航和内容抓取
```javascript
// AI Agent调用MCP-Chrome工具的示例流程
1. navigate_to_url("https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement")
2. wait_for_page_load()
3. extract_content("ul.list li")  // 提取公告列表
4. click_pagination_if_exists()   // 处理分页
5. extract_detailed_info()        // 提取详细信息
```

#### 3.2 数据提取策略
```yaml
提取规则:
  公告列表选择器: "ul.list li"
  标题选择器: "a[title]"
  时间选择器: ".time, .date"
  链接选择器: "a[href*='detail']"
  
智能提取:
  - 使用AI理解页面结构
  - 自动适应页面变化
  - 语义化内容分析
```

### 4. 数据处理和存储

#### 4.1 数据清洗和结构化
```javascript
// Code节点处理逻辑
const processData = (rawData) => {
  return rawData.map(item => ({
    title: item.title.trim(),
    publishTime: parseDate(item.time),
    department: extractDepartment(item.title),
    budget: extractBudget(item.content),
    detailUrl: item.link,
    type: item.category,
    extractTime: new Date().toISOString(),
    status: 'new'
  }));
};
```

#### 4.2 飞书API集成
```yaml
HTTP Request Node:
  Method: "POST"
  URL: "https://open.feishu.cn/open-apis/bitable/v1/apps/{app_token}/tables/{table_id}/records"
  Headers:
    Authorization: "Bearer {access_token}"
    Content-Type: "application/json"
  Body:
    records: "{{ $json.processedData }}"
```

## 优势分析

### 技术优势
1. **真实浏览器环境**：完美处理JavaScript渲染和动态内容
2. **智能适应性**：AI驱动的内容理解和提取
3. **反爬虫能力**：真实用户行为模拟，绕过检测
4. **可视化调试**：可以直接观察浏览器操作过程
5. **扩展性强**：易于适配其他网站

### 业务优势
1. **高准确性**：AI理解语义，减少误提取
2. **实时性**：定时监控，及时发现更新
3. **结构化存储**：直接输出到多维表格
4. **可追溯性**：完整的操作日志和数据来源

## 实施计划

### 第一阶段：环境搭建（1-2天）
- [ ] 安装配置n8n环境
- [ ] 部署mcp-chrome服务
- [ ] 配置飞书API权限
- [ ] 测试MCP连接

### 第二阶段：工作流开发（3-5天）
- [ ] 创建基础工作流
- [ ] 配置AI Agent和MCP Client
- [ ] 实现数据提取逻辑
- [ ] 集成飞书API

### 第三阶段：测试优化（2-3天）
- [ ] 功能测试和调试
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 监控告警配置

### 第四阶段：部署上线（1天）
- [ ] 生产环境部署
- [ ] 定时任务配置
- [ ] 运行监控
- [ ] 文档整理

## 风险控制

### 技术风险
1. **浏览器稳定性**：定期重启Chrome进程
2. **网络连接**：实现重试和降级机制
3. **内存泄漏**：监控资源使用，定期清理

### 业务风险
1. **数据准确性**：多重验证机制
2. **合规性**：遵守网站使用条款
3. **频率控制**：避免过于频繁的访问

## 成本估算

### 开发成本
- 开发时间：1-2周
- 人力成本：1名全栈开发工程师

### 运行成本
- 服务器：$30-50/月（包含Chrome运行环境）
- 飞书API：免费额度内
- 总计：$30-50/月

## 总结

基于n8n + MCP-Chrome的方案充分利用了真实浏览器环境的优势，结合AI的智能分析能力，为政府采购信息监控提供了一个强大、可靠、易维护的解决方案。该方案不仅技术先进，而且具有良好的扩展性和适应性，能够满足长期的业务需求。
