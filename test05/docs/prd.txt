# 智能网站内容监控与数据提取系统 - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目背景
用户需要自动化监控浙江政府采购网的招投标信息更新，由于更新时间不可预测，需要构建一个基于AI Agent的智能监控系统，实现自动化数据收集和结构化存储。

### 1.2 业务目标
- 实时监控政府采购网站的招投标信息更新
- 自动提取关键信息并结构化存储
- 提高信息获取效率，降低人工成本
- 为业务决策提供及时、准确的数据支持

### 1.3 目标网站
- **主站地址**：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement
- **监控页面1**：采购项目公告-招标公告
- **监控页面2**：采购结果公告-中标（成交）结果公告

### 1.4 技术方案选择
基于用户明确要求，采用 **n8n + MCP-Chrome** 技术方案：
- n8n工作流自动化平台作为核心调度引擎
- MCP-Chrome提供真实浏览器环境的内容抓取能力
- AI Agent进行智能内容分析和数据提取
- 飞书多维表格API实现结构化数据存储

## 2. 功能需求

### 2.1 核心功能模块

#### 2.1.1 定时监控模块
- **触发频率**：工作日（周一至周五）8:00-18:00，每30分钟执行一次
- **监控范围**：招标公告页面 + 中标结果页面
- **触发条件**：定时触发 + 手动触发支持

#### 2.1.2 智能抓取模块
- **浏览器环境**：使用MCP-Chrome提供真实Chrome浏览器环境
- **页面渲染**：完整支持JavaScript渲染和动态内容加载
- **交互能力**：支持页面导航、点击、滚动等用户行为模拟
- **反爬虫绕过**：真实用户行为模拟，绕过基础反爬虫检测

#### 2.1.3 内容提取模块
- **AI驱动提取**：使用AI Agent理解页面语义，智能提取关键信息
- **字段提取**：
  - 公告标题
  - 发布时间
  - 采购单位/部门
  - 项目预算金额
  - 公告详情链接
  - 公告类型（招标/中标）
  - 项目编号
  - 联系方式
- **数据清洗**：自动去重、格式标准化、数据验证

#### 2.1.4 数据存储模块
- **存储目标**：飞书多维表格
- **存储方式**：批量API调用，支持单次最多500条记录
- **数据结构**：结构化JSON格式
- **增量更新**：只存储新增或变更的公告信息

#### 2.1.5 监控告警模块
- **运行状态监控**：工作流执行状态、错误率、成功率
- **异常告警**：抓取失败、数据异常、API调用失败
- **告警方式**：邮件通知、钉钉/飞书消息推送

### 2.2 数据字段定义

#### 2.2.1 基础字段
```json
{
  "id": "唯一标识符",
  "title": "公告标题",
  "publishTime": "发布时间 (ISO 8601格式)",
  "department": "采购单位/部门",
  "budget": "项目预算金额",
  "detailUrl": "公告详情链接",
  "type": "公告类型 (招标/中标)",
  "projectNumber": "项目编号",
  "contact": "联系方式",
  "extractTime": "数据提取时间",
  "status": "处理状态 (new/processed/archived)"
}
```

#### 2.2.2 扩展字段
```json
{
  "category": "项目分类",
  "deadline": "截止时间",
  "location": "项目地点",
  "description": "项目描述摘要",
  "attachments": "附件链接列表",
  "keywords": "关键词标签"
}
```

## 3. 技术实现要求

### 3.1 系统架构
```
Schedule Trigger → AI Agent → MCP Client Tool → mcp-chrome → 数据处理 → 飞书API → 数据存储
```

### 3.2 技术组件

#### 3.2.1 n8n工作流配置
- **Schedule Trigger节点**：定时任务调度
- **AI Agent节点**：智能内容分析（Claude 3.5 Sonnet）
- **MCP Client Tool节点**：连接MCP-Chrome服务
- **Code节点**：数据处理和转换
- **HTTP Request节点**：飞书API调用
- **Error Trigger节点**：异常处理和重试

#### 3.2.2 MCP-Chrome服务
- **部署方式**：Chrome扩展 + 本地MCP服务器
- **服务端口**：3001（可配置）
- **通信协议**：SSE (Server-Sent Events)
- **功能接口**：
  - navigate_to_url()：页面导航
  - wait_for_page_load()：等待页面加载
  - extract_content()：内容提取
  - take_screenshot()：页面截图
  - click_element()：元素点击

#### 3.2.3 AI Agent配置
```yaml
Model: "Claude 3.5 Sonnet"
System Prompt: |
  你是一个专业的政府采购信息提取专家。
  
  任务目标：
  1. 监控浙江政府采购网的招标公告和中标结果
  2. 智能提取关键信息并结构化输出
  3. 确保数据准确性和完整性
  
  目标网站：
  - 招标公告：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyAnnouncement
  - 中标结果：https://zfcg.czt.zj.gov.cn/site/category?parentId=600007&childrenCode=ZcyResult
  
  提取规则：
  - 使用CSS选择器：ul.list li 定位公告列表
  - 提取标题、时间、链接等关键信息
  - 自动识别公告类型和重要字段
  - 输出标准JSON格式数据
```

### 3.3 环境要求

#### 3.3.1 服务器环境
- **操作系统**：Ubuntu 20.04+ / CentOS 8+
- **内存**：最低4GB，推荐8GB
- **存储**：最低20GB可用空间
- **网络**：稳定的互联网连接

#### 3.3.2 软件依赖
- **Node.js**：v18.0+
- **Chrome浏览器**：最新稳定版
- **n8n**：最新版本
- **mcp-chrome**：最新版本

#### 3.3.3 API权限
- **飞书开放平台**：
  - 应用创建和配置
  - 多维表格API权限
  - Access Token获取

## 4. 性能与质量要求

### 4.1 性能指标
- **响应时间**：单次完整抓取 < 5分钟
- **并发处理**：支持同时监控2个页面
- **数据吞吐**：每小时处理 > 100条记录
- **系统可用性**：> 99%

### 4.2 质量指标
- **数据准确率**：> 95%
- **抓取成功率**：> 90%
- **去重准确率**：> 99%
- **API调用成功率**：> 95%

### 4.3 可靠性要求
- **自动重试**：网络异常自动重试3次
- **降级策略**：MCP-Chrome失败时的备选方案
- **数据备份**：本地JSON文件备份
- **日志记录**：完整的操作日志和错误日志

## 5. 用户界面要求

### 5.1 n8n工作流界面
- **可视化流程图**：清晰展示数据流转过程
- **节点配置界面**：友好的参数配置界面
- **执行日志**：详细的执行历史和状态
- **错误提示**：明确的错误信息和解决建议

### 5.2 飞书多维表格界面
- **数据展示**：清晰的表格布局和字段显示
- **筛选排序**：支持按时间、类型、状态筛选
- **数据导出**：支持Excel、CSV格式导出
- **权限控制**：合理的查看和编辑权限设置

## 6. 安全与合规要求

### 6.1 数据安全
- **传输加密**：HTTPS协议传输
- **存储安全**：飞书平台安全保障
- **访问控制**：API密钥安全管理
- **数据脱敏**：敏感信息处理

### 6.2 合规要求
- **访问频率控制**：避免对目标网站造成过大压力
- **robots.txt遵守**：尊重网站爬虫协议
- **用户协议遵守**：符合网站使用条款
- **数据使用合规**：仅用于合法商业用途

## 7. 项目交付

### 7.1 交付物清单
- **n8n工作流文件**：完整的工作流JSON配置
- **MCP-Chrome部署包**：服务部署脚本和配置文件
- **飞书表格模板**：预配置的多维表格模板
- **部署文档**：详细的环境搭建和部署指南
- **操作手册**：系统使用和维护手册
- **测试报告**：功能测试和性能测试报告

### 7.2 验收标准
- **功能完整性**：所有需求功能正常运行
- **数据准确性**：抓取数据准确率达到95%以上
- **系统稳定性**：连续运行7天无重大故障
- **文档完整性**：文档齐全且可操作
- **用户培训**：完成用户操作培训

## 8. 项目计划与里程碑

### 8.1 开发阶段
- **第一阶段：环境搭建**（2天）
  - n8n环境部署
  - MCP-Chrome服务配置
  - 飞书API权限申请
  - 基础连通性测试

- **第二阶段：工作流开发**（5天）
  - AI Agent配置和调试
  - MCP Client Tool集成
  - 数据提取逻辑实现
  - 飞书API集成

- **第三阶段：测试优化**（3天）
  - 功能测试和调试
  - 性能优化和调整
  - 错误处理完善
  - 监控告警配置

- **第四阶段：部署上线**（1天）
  - 生产环境部署
  - 定时任务配置
  - 系统监控启动
  - 用户培训交付

### 8.2 关键里程碑
- **M1**：环境配置完成，基础服务正常运行
- **M2**：工作流开发完成，能够成功抓取数据
- **M3**：测试完成，系统稳定性验证通过
- **M4**：正式上线，用户验收通过

## 9. 风险管理

### 9.1 技术风险
- **风险1**：目标网站结构变更
  - **影响**：数据抓取失败
  - **缓解措施**：实现多重CSS选择器备选方案，AI自适应提取

- **风险2**：MCP-Chrome服务不稳定
  - **影响**：浏览器自动化失败
  - **缓解措施**：实现服务监控和自动重启机制

- **风险3**：飞书API限流
  - **影响**：数据存储失败
  - **缓解措施**：实现请求频率控制和重试机制

### 9.2 业务风险
- **风险1**：数据准确性问题
  - **影响**：业务决策错误
  - **缓解措施**：多重数据验证和人工抽检

- **风险2**：系统维护成本高
  - **影响**：运营成本超预算
  - **缓解措施**：自动化运维和监控告警

## 10. 成本预算

### 10.1 开发成本
- **人力成本**：1名全栈开发工程师 × 2周 = 2人周
- **开发工具**：n8n社区版（免费）
- **测试环境**：云服务器 × 2周 = $100

### 10.2 运行成本（月度）
- **服务器费用**：$40-60/月（包含Chrome运行环境）
- **飞书API费用**：免费额度内
- **网络带宽**：$10-20/月
- **总计**：$50-80/月

### 10.3 维护成本
- **日常维护**：0.2人天/月
- **功能迭代**：按需投入
- **紧急修复**：0.5人天/次

---

**项目成功标准**：构建一个稳定、可靠、智能的政府采购信息监控系统，实现自动化数据收集和结构化存储，数据准确率达到95%以上，系统可用性达到99%以上。
